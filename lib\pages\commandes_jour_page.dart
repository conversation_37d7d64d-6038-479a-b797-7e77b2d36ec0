import 'dart:io';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/colis.dart';
import '../services/colis_service.dart';
import '../services/pdf_service.dart';
import '../widgets/password_dialog.dart';
import 'ajouter_livraison_page.dart';

class CommandesJourPage extends StatefulWidget {
  final DateTime? dateSelectionnee;

  const CommandesJourPage({super.key, this.dateSelectionnee});

  @override
  State<CommandesJourPage> createState() => _CommandesJourPageState();
}

class _CommandesJourPageState extends State<CommandesJourPage> {
  final ColisService _colisService = ColisService.instance;
  final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');
  static final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');

  DateTime _dateSelectionnee = DateTime.now();
  List<Colis> _colis = [];
  StatistiquesLivraison? _statistiques;
  bool _isLoading = true;
  bool _isExporting = false;

  @override
  void initState() {
    super.initState();
    if (widget.dateSelectionnee != null) {
      _dateSelectionnee = widget.dateSelectionnee!;
    }
    _chargerDonnees();
  }

  Future<void> _chargerDonnees() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final colis = await _colisService.obtenirColisParDate(_dateSelectionnee);
      final stats = StatistiquesLivraison.fromColis(colis, _dateSelectionnee);

      if (mounted) {
        setState(() {
          _colis = colis;
          _statistiques = stats;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du chargement: $e')),
        );
      }
    }
  }

  Future<void> _selectionnerDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _dateSelectionnee,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('fr', 'FR'),
    );

    if (date != null && date != _dateSelectionnee) {
      setState(() {
        _dateSelectionnee = date;
      });
      await _chargerDonnees();
    }
  }

  Future<void> _modifierStatut(Colis colis) async {
    final colisModifie = await Navigator.of(context).push<Colis>(
      MaterialPageRoute(
        builder: (context) => AjouterLivraisonPage(colis: colis),
      ),
    );
    if (colisModifie != null) {
      await _chargerDonnees(); // Recharger les données
    }
  }

  void _supprimerLivraison(Colis colis) async {
    // Vérification du mot de passe avant suppression
    final passwordConfirmed = await PasswordDialog.show(
      context: context,
      title: 'Authentification requise',
      message:
          'Pour supprimer la livraison "${colis.libelle}", veuillez saisir le mot de passe administrateur.',
    );

    if (!passwordConfirmed) return;

    // Confirmation finale de suppression
    if (!mounted) return;
    final bool? confirmer = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmer la suppression'),
          content: Text(
            'Êtes-vous sûr de vouloir supprimer la livraison "${colis.libelle}" ?\n\nCette action est irréversible.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Annuler'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Supprimer'),
            ),
          ],
        );
      },
    );

    if (confirmer == true) {
      try {
        await _colisService.supprimerColis(colis.id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Livraison supprimée avec succès'),
              backgroundColor: Colors.green,
            ),
          );
          _chargerDonnees();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur lors de la suppression: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _exporterPDF() async {
    if (_statistiques == null || _colis.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Aucune donnée à exporter')));
      return;
    }

    setState(() {
      _isExporting = true;
    });

    try {
      final fileName =
          'livraisons_${_dateFormat.format(_dateSelectionnee).replaceAll('/', '_')}.pdf';

      // Générer le PDF avec la nouvelle méthode
      await PDFService.generateDeliveryListPDF(_colis, _dateSelectionnee);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('PDF exporté: $fileName'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Erreur lors de l\'export: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  Future<void> _partagerColisPDF(Colis colis) async {
    try {
      // Afficher un indicateur de chargement
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const AlertDialog(
            content: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('Génération du PDF...'),
              ],
            ),
          );
        },
      );

      // Générer le PDF du colis
      await PDFService.generateColisPDF(colis);

      // Fermer le dialog de chargement
      if (mounted) {
        Navigator.of(context).pop();

        // Afficher un message de succès
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('PDF généré pour le colis ${colis.numeroClient}'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () {},
            ),
          ),
        );
      }
    } catch (e) {
      // Fermer le dialog de chargement en cas d'erreur
      if (mounted) {
        Navigator.of(context).pop();

        // Afficher le message d'erreur
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la génération du PDF: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () {},
            ),
          ),
        );
      }
    }
  }

  Widget _buildStatistiquesCard() {
    if (_statistiques == null) return const SizedBox.shrink();

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  'Statistiques du jour',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Statistiques par statut
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total',
                    _statistiques!.totalColis.toString(),
                    Colors.blue,
                    Icons.inventory_2,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard(
                    'Livrés',
                    _statistiques!.livres.toString(),
                    Colors.green,
                    Icons.check_circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard(
                    'En retard',
                    _statistiques!.enRetard.toString(),
                    Colors.orange,
                    Icons.schedule,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Annulés',
                    _statistiques!.annules.toString(),
                    Colors.red,
                    Icons.cancel,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard(
                    'Reportés',
                    _statistiques!.reportes.toString(),
                    Colors.purple,
                    Icons.event_busy,
                  ),
                ),
                const SizedBox(width: 8),
                const Expanded(child: SizedBox()), // Espace vide
              ],
            ),

            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),

            // Statistiques financières
            Column(
              children: [
                _buildFinancialRow(
                  'Total à encaisser',
                  _statistiques!.totalAEncaisser,
                  Colors.blue[600]!,
                ),
                const SizedBox(height: 8),
                _buildFinancialRow(
                  'Total frais livraison',
                  _statistiques!.totalFraisLivraison,
                  Colors.orange[600]!,
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color:
                        _statistiques!.pointJournalier >= 0
                            ? Colors.green[50]
                            : Colors.red[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color:
                          _statistiques!.pointJournalier >= 0
                              ? Colors.green[200]!
                              : Colors.red[200]!,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Point journalier',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color:
                              _statistiques!.pointJournalier >= 0
                                  ? Colors.green[800]
                                  : Colors.red[800],
                        ),
                      ),
                      Text(
                        '${_currencyFormat.format(_statistiques!.pointJournalier)} FCFA',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color:
                              _statistiques!.pointJournalier >= 0
                                  ? Colors.green[800]
                                  : Colors.red[800],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(fontSize: 12, color: color),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialRow(String label, double amount, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(fontWeight: FontWeight.w500, color: color),
        ),
        Text(
          '${_currencyFormat.format(amount)} FCFA',
          style: TextStyle(fontWeight: FontWeight.bold, color: color),
        ),
      ],
    );
  }

  Widget _buildColisCard(Colis colis) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(6),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Photo du colis
            Container(
              width: 35,
              height: 35,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child:
                    colis.photoPath.isNotEmpty &&
                            File(colis.photoPath).existsSync()
                        ? Image.file(File(colis.photoPath), fit: BoxFit.cover)
                        : Icon(
                          Icons.inventory_2,
                          color: Colors.grey[400],
                          size: 20,
                        ),
              ),
            ),

            const SizedBox(width: 6),

            // Informations principales
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    colis.libelle,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          colis.nomClient ?? 'Client non renseigné',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 10,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        colis.numeroClient,
                        style: TextStyle(color: Colors.grey[600], fontSize: 10),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(width: 4),

            // Informations détaillées
            Expanded(
              flex: 2,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: _buildInfoChip(
                      Icons.location_on,
                      colis.zoneLivraison,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 2),
                  Expanded(
                    child: _buildInfoChip(
                      Icons.account_balance_wallet,
                      _currencyFormat.format(colis.resteAPayer),
                      Colors.green,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 4),

            // Statut
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: Color(
                  int.parse(colis.statut.couleur.replaceAll('#', '0xFF')),
                ).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: Color(
                    int.parse(colis.statut.couleur.replaceAll('#', '0xFF')),
                  ).withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    colis.statut.emoji,
                    style: const TextStyle(fontSize: 10),
                  ),
                  const SizedBox(width: 2),
                  Text(
                    colis.statut.libelle,
                    style: TextStyle(
                      color: Color(colis.statut.colorValue),
                      fontWeight: FontWeight.w500,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 4),

            // Boutons d'action
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  onPressed: () => _modifierStatut(colis),
                  icon: Icon(Icons.edit, size: 14, color: Colors.blue[600]),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 20,
                    minHeight: 20,
                  ),
                  tooltip: 'Modifier',
                ),
                IconButton(
                  onPressed: () => _partagerColisPDF(colis),
                  icon: Icon(
                    Icons.picture_as_pdf,
                    size: 14,
                    color: Colors.green[600],
                  ),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 20,
                    minHeight: 20,
                  ),
                  tooltip: 'PDF',
                ),
                IconButton(
                  onPressed: () => _supprimerLivraison(colis),
                  icon: Icon(Icons.delete, size: 14, color: Colors.red[600]),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 20,
                    minHeight: 20,
                  ),
                  tooltip: 'Supprimer',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String text, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 12, color: color), // Réduit de 14 à 12
        const SizedBox(width: 2), // Réduit de 4 à 2
        Flexible(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 11, // Réduit de 12 à 11
              color: color,
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Commandes du jour',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              _dateFormat.format(_dateSelectionnee),
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _selectionnerDate,
            icon: const Icon(Icons.calendar_today),
            tooltip: 'Changer la date',
          ),
          if (_colis.isNotEmpty)
            IconButton(
              onPressed: _isExporting ? null : _exporterPDF,
              icon:
                  _isExporting
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                      : const Icon(Icons.picture_as_pdf),
              tooltip: 'Exporter en PDF',
            ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // Statistiques
                  _buildStatistiquesCard(),

                  // Liste des colis
                  Expanded(
                    child:
                        _colis.isEmpty
                            ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.inventory_2_outlined,
                                    size: 64,
                                    color: Colors.grey[400],
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Aucune livraison pour cette date',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  TextButton.icon(
                                    onPressed: _selectionnerDate,
                                    icon: const Icon(Icons.calendar_today),
                                    label: const Text('Changer la date'),
                                  ),
                                ],
                              ),
                            )
                            : ListView.builder(
                              itemCount: _colis.length,
                              itemBuilder: (context, index) {
                                return _buildColisCard(_colis[index]);
                              },
                            ),
                  ),
                ],
              ),
    );
  }
}
