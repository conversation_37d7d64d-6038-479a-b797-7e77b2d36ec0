import 'dart:io';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:open_file/open_file.dart';
import 'package:share_plus/share_plus.dart';
import 'package:intl/intl.dart';
import '../models/invoice.dart';
import '../models/colis.dart';
import 'dart:math' as math;

class PDFService {
  static final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');
  static final DateFormat _dateFormat = DateFormat('dd/MM/yyyy', 'fr_FR');

  // Formate un montant sans espace insécable
  static String formatMontant(num value) {
    return _currencyFormat.format(value).replaceAll('\u00A0', ' ');
  }

  // Méthode pour générer le PDF sans le sauvegarder (pour prévisualisation)
  static Future<pw.Document> generateInvoicePDF(Invoice invoice) async {
    final pdf = pw.Document();

    // Charger le header en premier car il est async
    final header = await _buildHeader();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            header,
            pw.SizedBox(height: 30),
            _buildInvoiceInfo(invoice),
            pw.SizedBox(height: 20),
            _buildClientInfo(invoice),
            pw.SizedBox(height: 30),
            _buildItemsTable(invoice),
            pw.SizedBox(height: 20),
            _buildTotalSection(invoice, context), // Passer le context ici
            if (invoice.type == InvoiceType.proforma &&
                invoice.specialConditions != null &&
                invoice.specialConditions!.isNotEmpty)
              pw.SizedBox(height: 20),
            if (invoice.type == InvoiceType.proforma &&
                invoice.specialConditions != null &&
                invoice.specialConditions!.isNotEmpty)
              _buildSpecialConditionsSection(invoice.specialConditions!),
            if (invoice.notes != null && invoice.notes!.isNotEmpty)
              pw.SizedBox(height: 20),
            if (invoice.notes != null && invoice.notes!.isNotEmpty)
              _buildNotesSection(invoice.notes!),
            pw.SizedBox(height: 30),
            _buildFooter(invoice.footerNote),
          ];
        },
      ),
    );

    return pdf;
  }

  // Méthode pour générer et télécharger directement (ancienne méthode)
  static Future<void> generateAndDownloadInvoicePDF(Invoice invoice) async {
    final pdf = await generateInvoicePDF(invoice);
    await _saveAndOpenPDF(pdf, invoice);
  }

  static Future<pw.Widget> _buildHeader() async {
    // Charger le logo depuis les assets
    pw.ImageProvider? logoImage;
    try {
      final logoData = await rootBundle.load(
        'assets/images/logo_entreprise.png',
      );
      logoImage = pw.MemoryImage(logoData.buffer.asUint8List());
    } catch (e) {
      // Si le logo n'est pas trouvé, on continue sans logo
      logoImage = null;
    }

    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue900,
        borderRadius: pw.BorderRadius.circular(10),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'HCP-DESIGN',
                style: pw.TextStyle(
                  fontSize: 28,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.white,
                ),
              ),
              if (logoImage != null)
                pw.Container(
                  width: 80, // Agrandissement du logo
                  height: 80, // Agrandissement du logo
                  child: pw.Image(logoImage, fit: pw.BoxFit.contain),
                ),
            ],
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Téléphone: +225 07 09 49 58 48',
                    style: const pw.TextStyle(
                      fontSize: 12,
                      color: PdfColors.white,
                    ),
                  ),
                  pw.Text(
                    'Email: <EMAIL>',
                    style: const pw.TextStyle(
                      fontSize: 12,
                      color: PdfColors.white,
                    ),
                  ),
                ],
              ),
              pw.Text(
                'www.hcp-designci.com',
                style: const pw.TextStyle(fontSize: 12, color: PdfColors.white),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildInvoiceInfo(Invoice invoice) {
    final isProforma = invoice.type == InvoiceType.proforma;

    return pw.Column(
      children: [
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  isProforma ? 'FACTURE PROFORMA' : 'FACTURE',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                    color: isProforma ? PdfColors.orange800 : PdfColors.blue900,
                  ),
                ),
                if (isProforma)
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(top: 4),
                    child: pw.Text(
                      'Cette facture proforma ne vaut pas facture définitive',
                      style: pw.TextStyle(
                        fontSize: 10,
                        fontStyle: pw.FontStyle.italic,
                        color: PdfColors.orange600,
                      ),
                    ),
                  ),
                pw.SizedBox(height: 5),
                pw.Text(
                  'N° ${invoice.invoiceNumber}',
                  style: const pw.TextStyle(
                    fontSize: 14,
                    color: PdfColors.grey700,
                  ),
                ),
              ],
            ),
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  'Date: ${_dateFormat.format(invoice.createdAt)}',
                  style: const pw.TextStyle(fontSize: 12),
                ),
                if (isProforma && invoice.validityDate != null)
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(top: 5),
                    child: pw.Text(
                      'Valide jusqu\'au: ${_dateFormat.format(invoice.validityDate!)}',
                      style: pw.TextStyle(
                        fontSize: 12,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.orange700,
                      ),
                    ),
                  ),
                if (invoice.deliveryDetails != null &&
                    invoice.deliveryDetails!.isNotEmpty)
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(top: 5),
                    child: pw.Text(
                      'Détails livraison: ${invoice.deliveryDetails}',
                      style: const pw.TextStyle(
                        fontSize: 10,
                        color: PdfColors.grey700,
                      ),
                    ),
                  ),
                pw.SizedBox(height: 5),
                pw.Container(
                  padding: const pw.EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: pw.BoxDecoration(
                    color: _getStatusColor(invoice.status),
                    borderRadius: pw.BorderRadius.circular(4),
                  ),
                  child: pw.Text(
                    invoice.status.displayName,
                    style: const pw.TextStyle(
                      fontSize: 10,
                      color: PdfColors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        // Informations spécifiques aux factures proforma
        if (isProforma) ..._buildProformaSpecificInfo(invoice),
      ],
    );
  }

  static List<pw.Widget> _buildProformaSpecificInfo(Invoice invoice) {
    final widgets = <pw.Widget>[];

    if (invoice.companyRccm != null && invoice.companyRccm!.isNotEmpty ||
        invoice.companyTaxNumber != null &&
            invoice.companyTaxNumber!.isNotEmpty) {
      widgets.add(pw.SizedBox(height: 15));
      widgets.add(
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(12),
          decoration: pw.BoxDecoration(
            color: PdfColors.orange50,
            border: pw.Border.all(color: PdfColors.orange200),
            borderRadius: pw.BorderRadius.circular(6),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'INFORMATIONS ENTREPRISE',
                style: pw.TextStyle(
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.orange800,
                ),
              ),
              pw.SizedBox(height: 6),
              if (invoice.companyRccm != null &&
                  invoice.companyRccm!.isNotEmpty)
                pw.Text(
                  'N° RCCM: ${invoice.companyRccm}',
                  style: const pw.TextStyle(fontSize: 10),
                ),
              if (invoice.companyTaxNumber != null &&
                  invoice.companyTaxNumber!.isNotEmpty)
                pw.Text(
                  'N° Contribuable: ${invoice.companyTaxNumber}',
                  style: const pw.TextStyle(fontSize: 10),
                ),
            ],
          ),
        ),
      );
    }

    if (invoice.paymentMethods != null && invoice.paymentMethods!.isNotEmpty) {
      widgets.add(pw.SizedBox(height: 15));
      widgets.add(
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(12),
          decoration: pw.BoxDecoration(
            color: PdfColors.blue50,
            border: pw.Border.all(color: PdfColors.blue200),
            borderRadius: pw.BorderRadius.circular(6),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'MODES DE PAIEMENT ACCEPTÉS',
                style: pw.TextStyle(
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue800,
                ),
              ),
              pw.SizedBox(height: 6),
              pw.Text(
                invoice.paymentMethods!.join(' • '),
                style: const pw.TextStyle(fontSize: 10),
              ),
            ],
          ),
        ),
      );
    }

    return widgets;
  }

  static pw.Widget _buildClientInfo(Invoice invoice) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'INFORMATIONS CLIENT',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue900,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Text(
            'Nom: ${invoice.clientName}',
            style: const pw.TextStyle(fontSize: 12),
          ),
          pw.Text(
            'Téléphone: ${invoice.clientNumber}',
            style: const pw.TextStyle(fontSize: 12),
          ),
          if (invoice.clientAddress != null &&
              invoice.clientAddress!.isNotEmpty)
            pw.Text(
              'Adresse: ${invoice.clientAddress}',
              style: const pw.TextStyle(fontSize: 12),
            ),
          if (invoice.clientEmail != null && invoice.clientEmail!.isNotEmpty)
            pw.Text(
              'Email: ${invoice.clientEmail}',
              style: const pw.TextStyle(fontSize: 12),
            ),
          pw.Text(
            'Description: ${invoice.products}',
            style: const pw.TextStyle(fontSize: 12),
          ),
          pw.Text(
            'Zone de livraison: ${invoice.deliveryLocation}',
            style: const pw.TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildItemsTable(Invoice invoice) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'DÉTAIL DES ARTICLES',
          style: pw.TextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue900,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: {
            0: const pw.FlexColumnWidth(3),
            1: const pw.FlexColumnWidth(1),
            2: const pw.FlexColumnWidth(2),
            3: const pw.FlexColumnWidth(2),
          },
          children: [
            // En-tête du tableau
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey100),
              children: [
                _buildTableCell('Article', isHeader: true),
                _buildTableCell('Qté', isHeader: true),
                _buildTableCell('Prix unitaire', isHeader: true),
                _buildTableCell('Total', isHeader: true),
              ],
            ),
            // Lignes des articles
            ...invoice.items.map(
              (item) => pw.TableRow(
                children: [
                  _buildTableCell(item.name),
                  _buildTableCell(item.quantity.toString()),
                  _buildTableCell('${_currencyFormat.format(item.price)} FCFA'),
                  _buildTableCell('${_currencyFormat.format(item.total)} FCFA'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
      ),
    );
  }

  static pw.Widget _buildTotalSection(Invoice invoice, pw.Context context) {
    return pw.Container(
      width: double.infinity,
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.end,
        children: [
          pw.Container(
            width: 250,
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey300),
              borderRadius: pw.BorderRadius.circular(8),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                _buildTotalRow('Sous-total', invoice.subtotal, context),
                _buildTotalRow('Livraison', invoice.deliveryPrice, context),
                if (invoice.discountAmount >
                    0) // Ajout de l'affichage de la remise
                  _buildTotalRow(
                    'Remise',
                    -invoice.discountAmount,
                    context,
                    isDiscount: true,
                  ),
                if (invoice.advance > 0)
                  _buildTotalRow(
                    'Avance',
                    -invoice.advance,
                    context,
                    isAdvance: true,
                  ),
                pw.Divider(color: PdfColors.grey400, height: 20),
                _buildTotalRow('TOTAL', invoice.total, context, isTotal: true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildTotalRow(
    String label,
    double value,
    pw.Context context, {
    bool isTotal = false,
    bool isAdvance = false,
    bool isDiscount = false,
  }) {
    final PdfColor textColor =
        isAdvance
            ? PdfColors.orange700
            : isDiscount
            ? PdfColors.green700
            : PdfColors.black;
    final style =
        isTotal
            ? pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              fontSize: 14,
              color: PdfColors.blue900,
            )
            : pw.TextStyle(fontSize: 12, color: textColor);
    final valueStyle =
        isTotal
            ? pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              fontSize: 14,
              color: PdfColors.blue900,
            )
            : pw.TextStyle(
              fontSize: 12,
              color: textColor,
              fontWeight: pw.FontWeight.bold,
            );

    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(label, style: style),
          pw.Text(
            '${isDiscount ? '' : ''}${_currencyFormat.format(value)} FCFA',
            style: valueStyle,
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildSpecialConditionsSection(String specialConditions) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.orange50,
        border: pw.Border.all(color: PdfColors.orange300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'CONDITIONS SPÉCIALES',
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.orange800,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(specialConditions, style: const pw.TextStyle(fontSize: 10)),
        ],
      ),
    );
  }

  static pw.Widget _buildNotesSection(String notes) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey50,
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'NOTES',
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue900,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(notes, style: const pw.TextStyle(fontSize: 10)),
        ],
      ),
    );
  }

  static pw.Widget _buildFooter(String? customFooterNote) {
    final footerText =
        customFooterNote?.isNotEmpty == true
            ? customFooterNote!
            : 'Merci pour votre confiance - HCP-DESIGN';

    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: const pw.BoxDecoration(color: PdfColors.grey100),
      child: pw.Center(
        child: pw.Text(
          footerText,
          style: pw.TextStyle(
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue900,
          ),
        ),
      ),
    );
  }

  static PdfColor _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.payee:
        return PdfColors.green;

      case InvoiceStatus.enAttente:
        return PdfColors.orange;
      case InvoiceStatus.annulee:
        return PdfColors.red;
    }
  }

  static Future<void> _saveAndOpenPDF(pw.Document pdf, Invoice invoice) async {
    try {
      final prefix =
          invoice.type == InvoiceType.proforma ? 'Proforma' : 'Facture';
      final fileName =
          '${prefix}_${invoice.invoiceNumber}_${invoice.clientName.replaceAll(' ', '_')}.pdf';
      final bytes = await pdf.save();
      final dir = await getTemporaryDirectory();
      final file = File('${dir.path}/$fileName');
      await file.writeAsBytes(bytes);
      // Ouvrir le PDF
      await OpenFile.open(file.path);
      // Proposer de partager
      await Share.shareXFiles([XFile(file.path)], text: 'Voici le PDF généré');
    } catch (e) {
      throw Exception('Erreur lors de la génération du PDF: $e');
    }
  }

  // Méthode pour générer un PDF groupé sans le sauvegarder (pour prévisualisation)
  static Future<pw.Document> generateGroupedReceiptDocument(
    List<Invoice> invoices,
  ) async {
    final pdf = pw.Document();

    // Calculer le nombre de factures par page (max 6)
    // Les factures seront affichées dans une grille 3x2

    // Générer les pages avec les factures carrées (format simple)
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (pw.Context context) {
          // Créer uniquement la grille de factures carrées
          return _buildSquareInvoicesGrid(invoices);
        },
      ),
    );

    return pdf;
  }

  // Méthode pour générer un PDF groupé et le télécharger directement (ancienne méthode)
  static Future<void> generateGroupedReceiptPDF(List<Invoice> invoices) async {
    final pdf = await generateGroupedReceiptDocument(invoices);
    await _saveAndOpenGroupedPDF(pdf);
  }

  static pw.Widget _buildSquareInvoicesGrid(List<Invoice> invoices) {
    // Définir le nombre maximum de factures par page
    final int maxInvoicesPerPage = 6;
    final int totalInvoices = invoices.length;
    final int emptySlots = math.max(0, maxInvoicesPerPage - totalInvoices);

    // Calculer la disposition de la grille (3 colonnes, 2 lignes)
    final int columns = 3;
    final int rows = 2;

    // Créer une liste de widgets pour toutes les factures et les emplacements vides
    List<pw.Widget> invoiceWidgets = [];

    // Ajouter les factures existantes
    for (int i = 0; i < totalInvoices; i++) {
      invoiceWidgets.add(_buildSquareInvoice(invoices[i]));
    }

    // Ajouter les emplacements vides
    for (int i = 0; i < emptySlots; i++) {
      invoiceWidgets.add(_buildEmptySquareInvoice());
    }

    // Créer des lignes de grille
    List<pw.Widget> gridRows = [];
    for (int row = 0; row < rows; row++) {
      List<pw.Widget> rowChildren = [];
      for (int col = 0; col < columns; col++) {
        final int index = row * columns + col;
        if (index < invoiceWidgets.length) {
          rowChildren.add(
            pw.Expanded(
              child: pw.Padding(
                padding: const pw.EdgeInsets.all(5),
                child: invoiceWidgets[index],
              ),
            ),
          );
        }
      }

      gridRows.add(
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: rowChildren,
        ),
      );

      // Ajouter un espace entre les lignes
      if (row < rows - 1) {
        gridRows.add(pw.SizedBox(height: 10));
      }
    }

    return pw.Column(children: gridRows);
  }

  static pw.Widget _buildSquareInvoice(Invoice invoice) {
    // Debug: Vérifier les informations clients
    print('DEBUG PDF - Invoice ID: ${invoice.id}');
    print('DEBUG PDF - Client Name: "${invoice.clientName}"');
    print('DEBUG PDF - Client Number: "${invoice.clientNumber}"');
    print('DEBUG PDF - Delivery Location: "${invoice.deliveryLocation}"');
    
    return pw.Container(
      height: 200, // Hauteur fixe pour uniformité
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black, width: 2),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Padding(
        padding: const pw.EdgeInsets.all(8),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Header avec informations de l'entreprise HCP-DESIGN
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                // Logo HCP gauche
                pw.Row(
                  children: [
                    pw.Container(
                      width: 18,
                      height: 18,
                      decoration: pw.BoxDecoration(
                        color: PdfColors.blue,
                        shape: pw.BoxShape.circle,
                      ),
                      child: pw.Center(
                        child: pw.Text(
                          'C',
                          style: pw.TextStyle(
                            color: PdfColors.white,
                            fontSize: 9,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    pw.SizedBox(width: 2),
                    pw.Text(
                      'HCP',
                      style: pw.TextStyle(
                        fontSize: 12,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                // Informations entreprise droite
                pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    pw.Row(
                      mainAxisSize: pw.MainAxisSize.min,
                      children: [
                        pw.Container(
                          width: 10,
                          height: 10,
                          decoration: pw.BoxDecoration(
                            color: PdfColors.blue,
                            shape: pw.BoxShape.circle,
                          ),
                          child: pw.Center(
                            child: pw.Text(
                              'C',
                              style: pw.TextStyle(
                                color: PdfColors.white,
                                fontSize: 5,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        pw.SizedBox(width: 1),
                        pw.Text(
                          'Habillages et',
                          style: pw.TextStyle(fontSize: 6),
                        ),
                      ],
                    ),
                    pw.Text('Coque', style: pw.TextStyle(fontSize: 6)),
                    pw.Text('Personnalisés', style: pw.TextStyle(fontSize: 6)),
                    pw.Text(
                      '07 09 49 58 48',
                      style: pw.TextStyle(fontSize: 6, color: PdfColors.green, fontWeight: pw.FontWeight.bold),
                    ),
                    pw.Text(
                      'www.hcp-designci.com',
                      style: pw.TextStyle(fontSize: 5, color: PdfColors.blue),
                    ),
                  ],
                ),
              ],
            ),

            pw.SizedBox(height: 6),

            // Nom du client
            pw.Text(
              'Nom du client',
              style: pw.TextStyle(
                fontSize: 8,
                color: PdfColors.grey600,
                decoration: pw.TextDecoration.underline,
              ),
            ),
            pw.Text(
              invoice.clientName.isNotEmpty ? invoice.clientName : 'Non renseigné',
              style: pw.TextStyle(
                fontSize: 10, 
                fontWeight: pw.FontWeight.bold,
                color: invoice.clientName.isNotEmpty ? PdfColors.black : PdfColors.red,
              ),
            ),

            pw.SizedBox(height: 4),

            // Numéro du client
            pw.Text(
              'Numéro du client',
              style: pw.TextStyle(
                fontSize: 8,
                color: PdfColors.grey600,
                decoration: pw.TextDecoration.underline,
              ),
            ),
            pw.Text(
              invoice.clientNumber.isNotEmpty ? invoice.clientNumber : 'Non renseigné',
              style: pw.TextStyle(
                fontSize: 10, 
                fontWeight: pw.FontWeight.bold,
                color: invoice.clientNumber.isNotEmpty ? PdfColors.black : PdfColors.red,
              ),
            ),

            pw.SizedBox(height: 4),

            // Lieu de livraison
            pw.Text(
              'Lieu de livraison',
              style: pw.TextStyle(
                fontSize: 8,
                color: PdfColors.grey600,
                decoration: pw.TextDecoration.underline,
              ),
            ),
            pw.Text(
              invoice.deliveryLocation.isNotEmpty ? invoice.deliveryLocation : 'Non renseigné',
              style: pw.TextStyle(
                fontSize: 10, 
                fontWeight: pw.FontWeight.bold,
                color: invoice.deliveryLocation.isNotEmpty ? PdfColors.black : PdfColors.red,
              ),
            ),

            pw.Spacer(),

            // Reste à payer (au lieu de montant à payer)
            pw.Container(
              width: double.infinity,
              decoration: pw.BoxDecoration(
                color: PdfColors.grey400,
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(6)),
              ),
              padding: const pw.EdgeInsets.symmetric(
                vertical: 6,
                horizontal: 8,
              ),
              child: pw.Column(
                children: [
                  pw.Text(
                    'Reste à payer',
                    style: pw.TextStyle(
                      fontSize: 8,
                      color: PdfColors.grey700,
                      decoration: pw.TextDecoration.underline,
                    ),
                  ),
                  pw.Text(
                    '${NumberFormat('#,###').format(invoice.total - invoice.advance)} fr CFA',
                    style: pw.TextStyle(
                      fontSize: 12,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  static pw.Widget _buildEmptySquareInvoice() {
    return pw.Container(
      height: 200, // Même hauteur que les factures pleines
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black, width: 2),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Padding(
        padding: const pw.EdgeInsets.all(8),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Header avec informations de l'entreprise HCP-DESIGN (même que les factures pleines)
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                // Logo HCP gauche
                pw.Row(
                  children: [
                    pw.Container(
                      width: 18,
                      height: 18,
                      decoration: pw.BoxDecoration(
                        color: PdfColors.blue,
                        shape: pw.BoxShape.circle,
                      ),
                      child: pw.Center(
                        child: pw.Text(
                          'C',
                          style: pw.TextStyle(
                            color: PdfColors.white,
                            fontSize: 9,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    pw.SizedBox(width: 2),
                    pw.Text(
                      'HCP',
                      style: pw.TextStyle(
                        fontSize: 12,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                // Informations entreprise droite
                pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    pw.Row(
                      mainAxisSize: pw.MainAxisSize.min,
                      children: [
                        pw.Container(
                          width: 10,
                          height: 10,
                          decoration: pw.BoxDecoration(
                            color: PdfColors.blue,
                            shape: pw.BoxShape.circle,
                          ),
                          child: pw.Center(
                            child: pw.Text(
                              'C',
                              style: pw.TextStyle(
                                color: PdfColors.white,
                                fontSize: 5,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        pw.SizedBox(width: 1),
                        pw.Text(
                          'Habillages et',
                          style: pw.TextStyle(fontSize: 6),
                        ),
                      ],
                    ),
                    pw.Text('Coque', style: pw.TextStyle(fontSize: 6)),
                    pw.Text('Personnalisés', style: pw.TextStyle(fontSize: 6)),
                    pw.Text(
                      '07 09 49 58 48',
                      style: pw.TextStyle(fontSize: 6, color: PdfColors.green, fontWeight: pw.FontWeight.bold),
                    ),
                    pw.Text(
                      'www.hcp-designci.com',
                      style: pw.TextStyle(fontSize: 5, color: PdfColors.blue),
                    ),
                  ],
                ),
              ],
            ),

            pw.SizedBox(height: 6),

            // Espaces vides pour les informations
            pw.Text(
              'Nom du client',
              style: pw.TextStyle(
                fontSize: 8,
                color: PdfColors.grey600,
                decoration: pw.TextDecoration.underline,
              ),
            ),
            pw.SizedBox(height: 14),

            pw.Text(
              'Numéro du client',
              style: pw.TextStyle(
                fontSize: 8,
                color: PdfColors.grey600,
                decoration: pw.TextDecoration.underline,
              ),
            ),
            pw.SizedBox(height: 14),

            pw.Text(
              'Lieu de livraison',
              style: pw.TextStyle(
                fontSize: 8,
                color: PdfColors.grey600,
                decoration: pw.TextDecoration.underline,
              ),
            ),

            pw.Spacer(),

            // Reste à payer vide
            pw.Container(
              width: double.infinity,
              decoration: pw.BoxDecoration(
                color: PdfColors.grey400,
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(6)),
              ),
              padding: const pw.EdgeInsets.symmetric(
                vertical: 6,
                horizontal: 8,
              ),
              child: pw.Center(
                child: pw.Text(
                  'Reste à payer',
                  style: pw.TextStyle(
                    fontSize: 10,
                    color: PdfColors.grey700,
                    decoration: pw.TextDecoration.underline,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Future<void> _saveAndOpenGroupedPDF(pw.Document pdf) async {
    try {
      final fileName =
          'Recu_Groupe_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.pdf';
      final bytes = await pdf.save();
      final dir = await getTemporaryDirectory();
      final file = File('${dir.path}/$fileName');
      await file.writeAsBytes(bytes);
      // Ouvrir le PDF
      await OpenFile.open(file.path);
      // Proposer de partager
      await Share.shareXFiles([XFile(file.path)], text: 'Voici le reçu groupé');
    } catch (e) {
      throw Exception('Erreur lors de la génération du PDF groupé: $e');
    }
  }

  // Méthode pour générer le PDF des livraisons sans le sauvegarder (pour prévisualisation)
  static Future<pw.Document> generateDeliveryListDocument(
    List<dynamic> deliveries,
    DateTime date,
  ) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (pw.Context context) {
          return [
            // En-tête avec logo HCP
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                // Logo HCP (simulé avec du texte stylisé)
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.black, width: 2),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'HCP',
                        style: pw.TextStyle(
                          fontSize: 24,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        'DESIGN',
                        style: pw.TextStyle(
                          fontSize: 12,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        '07 09 49 58 48',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                      pw.Text(
                        'www.hcp-designci.com',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                    ],
                  ),
                ),
                // Date de livraison
                pw.Container(
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.black, width: 2),
                    borderRadius: pw.BorderRadius.circular(10),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'Date de livraison',
                        style: pw.TextStyle(
                          fontSize: 14,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        DateFormat('dd - MM - yyyy').format(date),
                        style: pw.TextStyle(
                          fontSize: 24,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            pw.SizedBox(height: 30),
            _buildDeliveryTable(deliveries),
          ];
        },
      ),
    );

    return pdf;
  }

  // Méthode pour générer le PDF des livraisons et le télécharger directement (ancienne méthode)
  static Future<void> generateDeliveryListPDF(
    List<dynamic> deliveries,
    DateTime date,
  ) async {
    final pdf = await generateDeliveryListDocument(deliveries, date);
    final fileName = 'livraisons_${DateFormat('dd_MM_yyyy').format(date)}.pdf';
    await _saveDeliveryPDF(pdf, fileName);
  }

  static pw.Widget _buildDeliveryTable(List<dynamic> deliveries) {
    // Calculer les totaux
    double totalValeurColis = deliveries.fold(
      0.0,
      (sum, delivery) => sum + (delivery.resteAPayer ?? 0),
    );
    double totalFraisLivraison = deliveries.fold(
      0.0,
      (sum, delivery) => sum + (delivery.fraisLivraison ?? 0),
    );
    double totalPourHCP = totalValeurColis - totalFraisLivraison;

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Tableau principal
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.black, width: 1),
          columnWidths: {
            0: const pw.FlexColumnWidth(3), // Numéro et Nom du client
            1: const pw.FlexColumnWidth(2), // Valeur du colis
            2: const pw.FlexColumnWidth(2), // Zone et prix
            3: const pw.FlexColumnWidth(2), // Montant
          },
          children: [
            // En-tête du tableau
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey300),
              children: [
                _buildDeliveryTableCell(
                  'Numéro et Nom du client',
                  isHeader: true,
                ),
                _buildDeliveryTableCell('Valeur du colis', isHeader: true),
                _buildDeliveryTableCell('Zone et prix', isHeader: true),
                _buildDeliveryTableCell('Montant', isHeader: true),
              ],
            ),
            // Lignes des livraisons
            ...deliveries.map(
              (delivery) => pw.TableRow(
                children: [
                  _buildDeliveryTableCell(
                    '${delivery.nomClient ?? 'Client'}\n${delivery.numeroClient}',
                  ),
                  _buildDeliveryTableCell(
                    _currencyFormat.format(delivery.resteAPayer ?? 0),
                  ),
                  _buildDeliveryTableCell(
                    '${delivery.zoneLivraison}\n${_currencyFormat.format(delivery.fraisLivraison ?? 0)}',
                  ),
                  _buildDeliveryTableCell(
                    _currencyFormat.format(delivery.fraisLivraison ?? 0),
                  ),
                ],
              ),
            ),
            // Ligne Total
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                _buildDeliveryTableCell('Total', isHeader: true),
                _buildDeliveryTableCell(
                  _currencyFormat.format(totalValeurColis),
                  isHeader: true,
                ),
                _buildDeliveryTableCell(
                  _currencyFormat.format(totalFraisLivraison),
                  isHeader: true,
                ),
                _buildDeliveryTableCell(
                  _currencyFormat.format(totalPourHCP),
                  isHeader: true,
                ),
              ],
            ),
          ],
        ),
        pw.SizedBox(height: 30),
        // Résumé
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(15),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.black, width: 2),
            borderRadius: pw.BorderRadius.circular(10),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'Résumé',
                style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 10),
              pw.Text('Nombre de colis : ${deliveries.length} colis'),
              pw.Text(
                'Valeur total des colis : ${formatMontant(totalValeurColis)} Fr CFA',
              ),
              pw.Text(
                'Frais de livraison : ${formatMontant(totalFraisLivraison)} Fr CFA',
              ),
              pw.Text(
                'Total pour HCP-DESIGN : ${formatMontant(totalPourHCP)} Fr CFA',
              ),
            ],
          ),
        ),
        pw.SizedBox(height: 20),
        // Total pour HCP-DESIGN
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(15),
          decoration: const pw.BoxDecoration(color: PdfColors.grey300),
          child: pw.Center(
            child: pw.Text(
              'Total pour HCP-DESIGN : ${formatMontant(totalPourHCP)} Fr CFA',
              style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
            ),
          ),
        ),
      ],
    );
  }

  static pw.Widget _buildDeliveryTableCell(
    String text, {
    bool isHeader = false,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: 9,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: isHeader ? pw.TextAlign.center : pw.TextAlign.left,
      ),
    );
  }

  static Future<void> _saveDeliveryPDF(pw.Document pdf, String fileName) async {
    try {
      final bytes = await pdf.save();
      final dir = await getTemporaryDirectory();
      final file = File('${dir.path}/$fileName');
      await file.writeAsBytes(bytes);
      await OpenFile.open(file.path);
      await Share.shareXFiles([XFile(file.path)], text: 'Liste des livraisons');
    } catch (e) {
      throw Exception('Erreur lors de la génération du PDF: $e');
    }
  }

  /// Génère un PDF pour un colis individuel sans le sauvegarder (pour prévisualisation)
  static Future<pw.Document> generateColisDocument(Colis colis) async {
    final pdf = pw.Document();

    // Charger l'image du colis si elle existe
    pw.ImageProvider? colisImage;
    try {
      if (colis.photoPath.isNotEmpty) {
        final imageFile = File(colis.photoPath);
        if (await imageFile.exists()) {
          final imageBytes = await imageFile.readAsBytes();
          colisImage = pw.MemoryImage(imageBytes);
        }
      }
    } catch (e) {
      // Si l'image n'est pas trouvée, on continue sans image
      colisImage = null;
    }

    // Charger le header
    final header = await _buildHeader();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              header,
              pw.SizedBox(height: 30),

              // Titre
              pw.Center(
                child: pw.Text(
                  'FICHE COLIS',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.blue900,
                  ),
                ),
              ),
              pw.SizedBox(height: 20),

              // Informations du colis
              pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Image du colis
                  pw.Expanded(
                    flex: 1,
                    child: pw.Container(
                      height: 200,
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(color: PdfColors.grey400),
                        borderRadius: pw.BorderRadius.circular(8),
                      ),
                      child:
                          colisImage != null
                              ? pw.Image(colisImage, fit: pw.BoxFit.cover)
                              : pw.Center(
                                child: pw.Column(
                                  mainAxisAlignment:
                                      pw.MainAxisAlignment.center,
                                  children: [
                                    pw.Icon(
                                      pw.IconData(0xe1c4), // Icons.inventory_2
                                      size: 48,
                                      color: PdfColors.grey400,
                                    ),
                                    pw.SizedBox(height: 8),
                                    pw.Text(
                                      'Aucune image',
                                      style: pw.TextStyle(
                                        color: PdfColors.grey600,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                    ),
                  ),
                  pw.SizedBox(width: 20),

                  // Détails du colis
                  pw.Expanded(
                    flex: 2,
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        _buildColisInfoRow('Libellé', colis.libelle),
                        _buildColisInfoRow('Numéro client', colis.numeroClient),
                        _buildColisInfoRow(
                          'Nom client',
                          colis.nomClient ?? 'Non renseigné',
                        ),
                        _buildColisInfoRow(
                          'Zone de livraison',
                          colis.zoneLivraison,
                        ),
                        _buildColisInfoRow(
                          'Adresse',
                          colis.adresseLivraison ?? 'Non renseignée',
                        ),
                        _buildColisInfoRow('Statut', colis.statut.libelle),
                        _buildColisInfoRow(
                          'Date d\'ajout',
                          _dateFormat.format(colis.dateAjout),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              pw.SizedBox(height: 30),

              // Informations financières
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  color: PdfColors.grey100,
                  borderRadius: pw.BorderRadius.circular(8),
                  border: pw.Border.all(color: PdfColors.grey300),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Informations financières',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.blue900,
                      ),
                    ),
                    pw.SizedBox(height: 12),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          'Reste à payer:',
                          style: const pw.TextStyle(fontSize: 14),
                        ),
                        pw.Text(
                          '${formatMontant(colis.resteAPayer)} FCFA',
                          style: pw.TextStyle(
                            fontSize: 14,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.green700,
                          ),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 8),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          'Frais de livraison:',
                          style: const pw.TextStyle(fontSize: 14),
                        ),
                        pw.Text(
                          '${formatMontant(colis.fraisLivraison)} FCFA',
                          style: pw.TextStyle(
                            fontSize: 14,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.orange700,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Notes si présentes
              if (colis.notes != null && colis.notes!.isNotEmpty)
                pw.SizedBox(height: 20),
              if (colis.notes != null && colis.notes!.isNotEmpty)
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(16),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.blue50,
                    borderRadius: pw.BorderRadius.circular(8),
                    border: pw.Border.all(color: PdfColors.blue200),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'Notes',
                        style: pw.TextStyle(
                          fontSize: 14,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.blue900,
                        ),
                      ),
                      pw.SizedBox(height: 8),
                      pw.Text(
                        colis.notes!,
                        style: const pw.TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),

              pw.Spacer(),

              // Footer
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(12),
                decoration: const pw.BoxDecoration(color: PdfColors.grey200),
                child: pw.Center(
                  child: pw.Text(
                    'Généré le ${_dateFormat.format(DateTime.now())} - HCP-DESIGN',
                    style: pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );

    return pdf;
  }

  /// Génère un PDF pour un colis individuel et le télécharge directement (ancienne méthode)
  static Future<void> generateColisPDF(Colis colis) async {
    final pdf = await generateColisDocument(colis);
    final fileName =
        'colis_${colis.numeroClient}_${DateTime.now().millisecondsSinceEpoch}.pdf';
    await _saveColisPDF(pdf, fileName);
  }

  static pw.Widget _buildColisInfoRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 8),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.SizedBox(
            width: 120,
            child: pw.Text(
              '$label:',
              style: pw.TextStyle(
                fontSize: 12,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.grey700,
              ),
            ),
          ),
          pw.Expanded(
            child: pw.Text(value, style: const pw.TextStyle(fontSize: 12)),
          ),
        ],
      ),
    );
  }

  static Future<void> _saveColisPDF(pw.Document pdf, String fileName) async {
    try {
      final bytes = await pdf.save();
      final dir = await getTemporaryDirectory();
      final file = File('${dir.path}/$fileName');
      await file.writeAsBytes(bytes);
      await OpenFile.open(file.path);
      await Share.shareXFiles([XFile(file.path)], text: 'Fiche colis');
    } catch (e) {
      throw Exception('Erreur lors de la génération du PDF: $e');
    }
  }
}
